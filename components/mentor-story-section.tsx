"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { fadeInUp, staggerContainer, staggerItem } from "@/lib/animations"

interface StoryCard {
  image: string
  title: string
  description: string
  highlight: string
  position: "top-left" | "top-right" | "bottom-center"
}

const storyCards: StoryCard[] = [
  {
    image: "/mentor-house.jpg",
    title: "In just a few years, I've seen my efforts translate into a",
    description: "multi-millionaire portfolio",
    highlight: "multi-millionaire",
    position: "top-left"
  },
  {
    image: "/mentor-crowd.jpg", 
    title: "Swing Trading Lab Students have reported generating over",
    description: "$3M combined in the last 3 years",
    highlight: "$3M combined",
    position: "top-right"
  },
  {
    image: "/mentor-cars.jpg",
    title: "I TURNED $100 ACCOUNT INTO",
    description: "$1,000,000 ACCOUNT IN JUST 4 MONTHS",
    highlight: "$1,000,000 ACCOUNT",
    position: "bottom-center"
  }
]

export function MentorStorySection() {
  return (
    <div className="py-24 relative overflow-hidden bg-black">
      {/* Subtle grid background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(74, 222, 128, 0.2) 1px, transparent 1px),
            linear-gradient(90deg, rgba(74, 222, 128, 0.2) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          {...fadeInUp}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="typography-h2 mb-4 tracking-tight text-center">
            THESE ARE THE RESULTS OF <span className="text-primary">MY STRATEGY</span>
          </h2>
        </motion.div>

        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto"
        >
          {/* Top Left Card */}
          <motion.div
            variants={staggerItem}
            className="relative group"
          >
            <div className="relative aspect-[4/3] overflow-hidden border border-primary/30 bg-black hover:border-primary/60 transition-all duration-300">
              <Image
                src={storyCards[0].image}
                alt="Mentor success story"
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-all duration-300" />
              
              {/* Text overlay */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-black/80 border border-primary/50 p-4 backdrop-blur-sm">
                  <p className="typography-body-s text-white/90 mb-2">
                    {storyCards[0].title}
                  </p>
                  <p className="typography-body-l text-primary font-semibold">
                    {storyCards[0].description}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Top Right Card */}
          <motion.div
            variants={staggerItem}
            className="relative group"
          >
            <div className="relative aspect-[4/3] overflow-hidden border border-primary/30 bg-black hover:border-primary/60 transition-all duration-300">
              <Image
                src={storyCards[1].image}
                alt="Student success story"
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-all duration-300" />
              
              {/* Text overlay */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-black/80 border border-primary/50 p-4 backdrop-blur-sm">
                  <p className="typography-body-s text-white/90 mb-2">
                    {storyCards[1].title}
                  </p>
                  <p className="typography-body-l text-primary font-semibold">
                    {storyCards[1].description}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Bottom Center Card - Full Width */}
          <motion.div
            variants={staggerItem}
            className="lg:col-span-2 relative group"
          >
            <div className="relative aspect-[16/9] lg:aspect-[21/9] overflow-hidden border border-primary/30 bg-black hover:border-primary/60 transition-all duration-300">
              <Image
                src={storyCards[2].image}
                alt="Trading transformation story"
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-black/50 group-hover:bg-black/40 transition-all duration-300" />
              
              {/* Centered text overlay */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-black/80 border border-primary/50 p-6 lg:p-8 backdrop-blur-sm text-center max-w-2xl mx-4">
                  <p className="typography-body-l text-white/90 mb-3 font-mono uppercase tracking-wider">
                    {storyCards[2].title}
                  </p>
                  <p className="typography-h2 text-primary font-bold">
                    {storyCards[2].description}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Optional bottom text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="typography-body-l text-white/70 max-w-3xl mx-auto">
            These results speak for themselves. The strategy works when applied correctly, 
            and I'm here to teach you exactly how to replicate this success.
          </p>
        </motion.div>
      </div>
    </div>
  )
}
