"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { fadeInUp, staggerContainer, staggerItem } from "@/lib/animations"

interface StoryStep {
  image: string
  chapter: string
  title: string
  description: string
  stats: string
  year: string
}

const storySteps: StoryStep[] = [
  {
    image: "/mentor-house.jpg",
    chapter: "Chapter 1",
    title: "The Beginning",
    description: "Started with a simple goal: financial freedom. Through disciplined strategy and relentless learning, I transformed my approach to trading.",
    stats: "Multi-millionaire portfolio",
    year: "2019-2023"
  },
  {
    image: "/mentor-crowd.jpg",
    chapter: "Chapter 2",
    title: "Sharing Knowledge",
    description: "Success isn't just personal. I began teaching others, creating the Swing Trading Lab. Watching students achieve their dreams became my new passion.",
    stats: "$3M+ combined student earnings",
    year: "2021-2024"
  },
  {
    image: "/mentor-cars.jpg",
    chapter: "Chapter 3",
    title: "Proving the System",
    description: "To demonstrate the power of the strategy, I took on the ultimate challenge: turning a small account into life-changing wealth.",
    stats: "$100 → $1,000,000",
    year: "4 months"
  }
]

export function MentorStorySection() {
  return (
    <div className="py-32 relative overflow-hidden bg-black">
      {/* Enhanced background with floating elements */}
      <div className="absolute inset-0">
        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(74, 222, 128, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(74, 222, 128, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '80px 80px'
          }} />
        </div>

        {/* Floating animated elements */}
        <motion.div
          className="absolute top-20 left-10 w-2 h-2 bg-primary/40 rounded-full"
          animate={{
            y: [0, -20, 0],
            opacity: [0.4, 0.8, 0.4]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-20 w-3 h-3 bg-primary/30 rounded-full"
          animate={{
            y: [0, -30, 0],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
        <motion.div
          className="absolute bottom-40 left-1/4 w-1.5 h-1.5 bg-primary/50 rounded-full"
          animate={{
            y: [0, -15, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
      </div>

      <div className="container mx-auto px-6 relative z-10 max-w-4xl">
        <motion.div
          {...fadeInUp}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="inline-block mb-6 px-6 py-2 border border-primary/60 bg-black/50 backdrop-blur-sm"
          >
            <span className="typography-body-s text-primary font-medium uppercase tracking-wider">My Journey</span>
          </motion.div>

          <h2 className="typography-h2 mb-6 tracking-tight text-center">
            THIS IS <span className="text-primary">MY STORY</span>
          </h2>
          <p className="typography-body-l text-white/70 max-w-2xl mx-auto">
            From struggling trader to mentor of thousands. Here's how I built a system that changed everything.
          </p>
        </motion.div>

        {/* Story Timeline */}
        <div className="space-y-32">
          {storySteps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.8,
                delay: index * 0.2,
                ease: "easeOut"
              }}
              viewport={{ once: true, margin: "-100px" }}
              className="relative"
            >
              {/* Timeline connector */}
              {index < storySteps.length - 1 && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  whileInView={{ height: "8rem", opacity: 1 }}
                  transition={{ duration: 1.2, delay: 0.8 + index * 0.3 }}
                  viewport={{ once: true }}
                  className="absolute left-1/2 -translate-x-0.5 bottom-0 w-0.5 bg-gradient-to-b from-primary/80 via-primary/40 to-transparent z-10"
                  style={{ top: "100%" }}
                />
              )}

              {/* Timeline dot */}
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.2 }}
                viewport={{ once: true }}
                className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-primary border-2 border-black rounded-full z-20"
              >
                <div className="absolute inset-1 bg-primary/60 rounded-full animate-pulse" />
              </motion.div>

              <div className={`flex flex-col lg:flex-row items-center gap-12 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}>
                {/* Image Section */}
                <motion.div
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="lg:w-1/2 relative group"
                >
                  <div className="relative aspect-[4/3] overflow-hidden border border-primary/30 bg-black hover:border-primary/60 transition-all duration-500 group">
                    <Image
                      src={step.image}
                      alt={step.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-all duration-500" />

                    {/* Floating stats overlay */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, delay: 0.8 }}
                      viewport={{ once: true }}
                      className="absolute top-6 right-6"
                    >
                      <div className="bg-black/90 border border-primary/60 px-4 py-2 backdrop-blur-sm">
                        <span className="typography-body-s text-primary font-mono font-bold">
                          {step.year}
                        </span>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>

                {/* Content Section */}
                <motion.div
                  initial={{ opacity: 0, x: index % 2 === 0 ? 50 : -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                  viewport={{ once: true }}
                  className="lg:w-1/2 text-center lg:text-left"
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.7 }}
                    viewport={{ once: true }}
                    className="inline-block mb-4 px-4 py-1.5 border border-primary/40 bg-primary/10 backdrop-blur-sm"
                  >
                    <span className="typography-body-s text-primary font-medium uppercase tracking-wider">
                      {step.chapter}
                    </span>
                  </motion.div>

                  <motion.h3
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                    viewport={{ once: true }}
                    className="typography-h2 mb-6 text-white"
                  >
                    {step.title}
                  </motion.h3>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.9 }}
                    viewport={{ once: true }}
                    className="typography-body-l text-white/80 mb-8 leading-relaxed"
                  >
                    {step.description}
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 1 }}
                    viewport={{ once: true }}
                    className="inline-block bg-black border border-primary/50 px-6 py-4 hover:border-primary/80 transition-all duration-300"
                  >
                    <span className="typography-stat-small text-primary font-mono">
                      {step.stats}
                    </span>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-24"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-black via-primary/5 to-black border border-primary/30 p-8 lg:p-12 backdrop-blur-sm"
          >
            <h3 className="typography-h2 mb-6 text-white">
              Your Story <span className="text-primary">Starts Here</span>
            </h3>
            <p className="typography-body-l text-white/80 max-w-2xl mx-auto mb-8 leading-relaxed">
              This isn't just my story—it's proof of what's possible. The same strategies, the same system,
              the same potential for transformation. Your chapter begins now.
            </p>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block"
            >
              <div className="bg-primary text-black px-8 py-4 font-mono font-bold uppercase tracking-wider hover:bg-primary/90 transition-all duration-300 cursor-pointer">
                Start Your Journey
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
